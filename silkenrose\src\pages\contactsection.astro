---
import Welcome from '../components/Welcome.astro';
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.astro';
import Contact from '../components/Contact.astro';
import Footer from '../components/Footer.astro';

// Welcome to Astro! Wondering what to do next? Check out the Astro documentation at https://docs.astro.build
// Don't want to use any of this? Delete everything in this file, the `assets`, `components`, and `layouts` directories, and start fresh.
---

<Layout>
	<Navigation/>
    <Contact/>
    <Footer/>
    
</Layout>
<style>:root {
    /* Peach-themed color palette perfect for cosmetics website */
    /* Add these styles to your global stylesheet, which is used across all site pages. You only need to do this once. All elements in the library derive their variables and base styles from this central sheet, simplifying site-wide edits. For instance, if you want to modify how your h2's appear across the site, you just update it once in the global styles, and the changes apply everywhere. */
    --primary: #FF9A8B;        /* Soft peach - main brand color */
    --primaryLight: #FFB5A7;   /* Light peach - hover states and accents */
    --secondary: #F4A6CD;      /* Soft pink - secondary actions */
    --secondaryLight: #F8C2D4; /* Light pink - subtle highlights */
    --headerColor: #2D1B1B;    /* Dark brown - elegant headers */
    --bodyTextColor: #5D4E4E;  /* Medium brown - readable body text */
    --bodyTextColorWhite: #FFF8F6; /* Warm white - text on dark backgrounds */
    /* 13px - 16px */
    --topperFontSize: clamp(0.8125rem, 1.6vw, 1rem);
    /* 31px - 49px */
    --headerFontSize: clamp(1.9375rem, 3.9vw, 3.0625rem);
    --bodyFontSize: 1rem;
    /* 60px - 100px top and bottom */
    --sectionPadding: clamp(3.75rem, 7.82vw, 6.25rem) 1rem;
}

body {
    margin: 0;
    padding: 0;
}

*, *:before, *:after {
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
}
.cs-topper {
    font-size: var(--topperFontSize);
    line-height: 1.2em;
    text-transform: uppercase;
    text-align: inherit;
    letter-spacing: .1em;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.25rem;
    display: block;
}

.cs-title {
    font-size: var(--headerFontSize);
    font-weight: 900;
    line-height: 1.2em;
    text-align: inherit;
    max-width: 43.75rem;
    margin: 0 0 1rem 0;
    color: var(--headerColor);
    position: relative;
}

.cs-text {
    font-size: var(--bodyFontSize);
    line-height: 1.5em;
    text-align: inherit;
    width: 100%;
    max-width: 40.625rem;
    margin: 0;
    color: var(--bodyTextColor);
}
</style>