<!-- ============================================ -->
<!--                    FAQ                       -->
<!-- ============================================ -->

<section id="faq-1504">
    <div class="cs-container">
        <div class="cs-content">
            <span class="cs-topper">Frequently asked questions</span>
            <h2 class="cs-title">Your Questions About Luxury Skincare, Answered</h2>
            <div class="cs-image-group">
                <picture class="cs-picture">
                    <!--Mobile Image-->
                    <source media="(max-width: 600px)" srcset="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=586&h=376&fit=crop&crop=center">
                    <!--Tablet and above Image-->
                    <source media="(min-width: 601px)" srcset="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=586&h=376&fit=crop&crop=center">
                    <img loading="lazy" decoding="async" src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=586&h=376&fit=crop&crop=center" alt="luxury cosmetics collection" width="586" height="376">
                </picture>
                <!--SVG Mask-->
                <svg class="cs-mask" id="Layer_2-1504" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 586 156"><defs><style>#faq .cls-1{fill:var(--maskColor);stroke-width:0px;}</style></defs><g id="Layer_1-2-1504"><path class="cls-1" d="m586,0H0c5.17,18.67,32.4,56,100,56h386c55.23,0,100,44.77,100,100V0Z"/></g></svg>
            </div>
        </div>
        <div class="cs-flex-group">
            <ul class="cs-faq-group">
                <!-- Active class added as default -->
                <li class="cs-faq-item active">
                    <button class="cs-button">
                        <span class="cs-button-text">
                            Are Pureluxus products suitable for sensitive skin?
                        </span>
                        <span class="cs-indicator" aria-hidden="true"></span>
                    </button>
                    <p class="cs-item-p">
                        Absolutely! All Pureluxus formulations are dermatologist-tested and clinically proven to be gentle yet effective. We carefully select hypoallergenic ingredients and avoid harsh chemicals, making our products suitable for even the most sensitive skin types. Each product undergoes rigorous testing to ensure safety and efficacy.
                    </p>
                </li>
                <li class="cs-faq-item">
                    <button class="cs-button">
                        <span class="cs-button-text">
                            What makes Pureluxus different from other luxury skincare brands?
                        </span>
                        <span class="cs-indicator" aria-hidden="true"></span>
                    </button>
                    <p class="cs-item-p">
                        Pureluxus combines the best of nature and science. We source rare, ethically-harvested botanical ingredients from pristine environments worldwide and blend them with cutting-edge peptides and antioxidants. Our commitment to being 100% cruelty-free, vegan, and sustainable sets us apart, along with our focus on delivering visible, transformative results through luxurious sensorial experiences.
                    </p>
                </li>
                <li class="cs-faq-item">
                    <button class="cs-button">
                        <span class="cs-button-text">
                            How long does it take to see results from Pureluxus products?
                        </span>
                        <span class="cs-indicator" aria-hidden="true"></span>
                    </button>
                    <p class="cs-item-p">
                        While individual results may vary, most clients notice immediate improvements in skin texture and hydration after the first application. For more significant transformations such as reduced fine lines, improved firmness, and enhanced radiance, we recommend consistent use for 4-6 weeks. Our clinically-tested formulations are designed to deliver both instant and long-term benefits.
                    </p>
                </li>
                <li class="cs-faq-item">
                    <button class="cs-button">
                        <span class="cs-button-text">
                            Are your products cruelty-free and vegan?
                        </span>
                        <span class="cs-indicator" aria-hidden="true"></span>
                    </button>
                    <p class="cs-item-p">
                        Yes! Pureluxus is proud to be 100% cruelty-free and vegan. We never test on animals, and none of our products contain animal-derived ingredients. We believe that true luxury should never come at the expense of our planet or its creatures. All our ingredients are ethically sourced and sustainably harvested.
                    </p>
                </li>
                <li class="cs-faq-item">
                    <button class="cs-button">
                        <span class="cs-button-text">
                            What is your return and satisfaction guarantee policy?
                        </span>
                        <span class="cs-indicator" aria-hidden="true"></span>
                    </button>
                    <p class="cs-item-p">
                        We stand behind the quality and efficacy of our products. If you're not completely satisfied with your Pureluxus purchase, we offer a 30-day money-back guarantee. Simply contact our customer care team, and we'll process your return with no questions asked. Your satisfaction and trust are our top priorities.
                    </p>
                </li>
            </ul>
        </div>
    </div>
    <!--Red Graphic-->
    <img class="cs-floater cs-floater1" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Graphics/red-squiggle2.svg" alt="graphic" loading="lazy" decoding="async" width="312" height="222" aria-hidden="true">
    <!--Leaf-->
    <img class="cs-floater cs-floater2" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Graphics/leaf2.svg" alt="graphic" loading="lazy" decoding="async" width="207" height="211" aria-hidden="true">
    <!--Top SVG zig zag-->
    <svg class="cs-top" width="1920" height="39" viewBox="0 0 1920 39" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1920 -0.388672H0V15.4329L4.95532 13.0079L18.1455 14.8914L28.6394 22.2058L42.0481 24.0343L54.0723 26.6713L66.8979 20.9736L78.7034 23.226L90.9463 27.4247L102.533 21.1855L114.776 24.1442L126.654 24.8427L138.605 24.411L148.881 28.7667L160.759 28.233L171.18 32.9654L183.569 26.7811L194.573 29.575L207.69 29.63L219.277 25.3293L228.531 18.5486L240.556 15.6448L253.017 13.0079L265.989 12.6861L279.033 14.138L292.296 15.8096L305.413 12.1446L317.437 16.6651L331.065 14.6795L342.943 18.7683L354.749 22.6374L366.773 25.7609L378.943 28.8766L391.331 31.3016L405.469 27.6915L417.274 31.6783L428.57 34.739L440.375 34.9038L451.598 38.0744L463.622 37.321L475.208 38.5061L487.087 35.3355L498.455 36.4107L510.115 37.7527L522.285 33.8287L536.058 31.6234L547.864 25.9179L561.928 28.8766L574.171 20.3772L587.434 21.6172L599.604 23.6576L611.191 21.1306L623.215 24.2541L634.947 24.8976L646.607 23.5556L658.413 20.1103L670.437 17.3164L681.805 24.0343L694.048 20.3772L706.072 19.7336L728.226 29.0414L741.197 24.2541H753.221L761.31 35.2805L773.771 33.7737L785.65 29.4181L796.29 30.87L809.261 34.2054L818.37 26.3495L828.864 21.7741L842.418 22.5825L852.329 16.6651L866.248 18.9253L875.722 11.399L887.673 9.56261L900.28 10.8575L911.94 7.89883L924.474 9.93931L937.373 7.78896L949.98 6.76871L961.931 9.92362L966.887 7.50643L980.077 14.7658L990.57 22.0802L1003.98 23.9088L1016 26.5457L1028.83 20.848L1040.63 23.1004L1052.88 27.2991L1064.46 21.0599L1076.71 24.0186L1088.59 24.7171L1100.54 24.2855L1110.81 28.6411L1122.69 28.1075L1133.11 32.8398L1145.5 26.6556L1156.5 29.4495L1169.62 29.5044L1181.21 25.2037L1190.46 18.423L1202.49 15.5192L1214.95 12.8823L1227.92 12.5605L1240.96 14.0124L1254.23 15.6841L1267.34 12.019L1279.37 16.5395L1293 14.5539L1304.87 18.6349L1316.68 22.5118L1328.7 25.6353L1340.87 28.751L1353.26 31.176L1367.4 27.5659L1379.21 31.5527L1390.5 34.6135L1402.31 34.7783L1413.53 37.9489L1425.55 37.1955L1437.14 38.3805L1449.02 35.2099L1460.39 36.2851L1472.05 37.6271L1484.22 33.7031L1497.99 31.4978L1509.79 25.7923L1523.86 28.751L1536.1 20.2516L1549.37 21.4916L1561.54 23.5321L1573.12 21.005L1585.15 24.1285L1596.88 24.7721L1608.54 23.43L1620.34 19.9848L1632.37 17.183L1643.74 23.9088L1655.98 20.2516L1668 19.6081L1690.16 28.9158L1703.13 24.1285H1715.15L1723.24 35.155L1735.7 33.6482L1747.58 29.2925L1758.22 30.7444L1771.19 34.0798L1780.3 26.2239L1790.79 21.6485L1804.35 22.4569L1814.26 16.5395L1828.18 18.7997L1837.65 11.2656L1849.6 9.43704L1862.21 10.732L1873.87 7.77326L1886.41 9.81374L1899.3 7.66339L1911.91 6.64315L1920 7.39656V-0.388672Z" fill="white"/>
        </svg>
    <!--Bottom SVG zig zag-->
    <svg class="cs-bottom" width="1900" height="39" viewBox="0 0 1900 39" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1900 38.8948H-20V23.0732L-15.0447 25.4982L-1.85449 23.6147L8.6394 16.3004L22.0481 14.4718L34.0723 11.8348L46.8979 17.5325L58.7034 15.2801L70.9463 11.0814L82.5332 17.3206L94.7759 14.3619L106.654 13.6634L118.605 14.0951L128.881 9.73941L140.759 10.2731L151.18 5.54073L163.569 11.725L174.573 8.93107L187.69 8.87613L199.277 13.1768L208.531 19.9575L220.556 22.8613L233.017 25.4982L245.989 25.82L259.033 24.3681L272.296 22.6965L285.413 26.3615L297.437 21.841L311.065 23.8266L322.943 19.7378L334.749 15.8687L346.773 12.7452L358.943 9.62954L371.331 7.20451L385.469 10.8146L397.274 6.8278L408.57 3.76708L420.375 3.60227L431.598 0.431671L443.622 1.18508L455.208 3.05176e-05L467.087 3.17063L478.455 2.09545L490.115 0.753441L502.285 4.67744L516.058 6.88274L527.864 12.5882L541.928 9.62954L554.171 18.1289L567.434 16.889L579.604 14.8485L591.191 17.3755L603.215 14.252L614.947 13.6085L626.607 14.9505L638.413 18.3958L650.437 21.1897L661.805 14.4718L674.048 18.1289L686.072 18.7725L708.226 9.46473L721.197 14.252H733.221L741.31 3.22556L753.771 4.73238L765.65 9.08803L776.29 7.63615L789.261 4.30074L798.37 12.1566L808.864 16.732L822.418 15.9236L832.329 21.841L846.248 19.5808L855.722 27.1071L867.673 28.9435L880.28 27.6486L891.94 30.6073L904.474 28.5668L917.373 30.7171L929.98 31.7374L941.931 28.5825L946.887 30.9997L960.077 23.7403L970.57 16.4259L983.979 14.5973L996.003 11.9604L1008.83 17.6581L1020.63 15.4057L1032.88 11.207L1044.46 17.4462L1056.71 14.4875L1068.59 13.789L1080.54 14.2206L1090.81 9.86498L1102.69 10.3986L1113.11 5.66629L1125.5 11.8505L1136.5 9.05664L1149.62 9.0017L1161.21 13.3024L1170.46 20.0831L1182.49 22.9869L1194.95 25.6238L1207.92 25.9456L1220.96 24.4937L1234.23 22.822L1247.34 26.4871L1259.37 21.9666L1273 23.9522L1284.87 19.8712L1296.68 15.9943L1308.7 12.8708L1320.87 9.75511L1333.26 7.33007L1347.4 10.9402L1359.21 6.95337L1370.5 3.89264L1382.31 3.72784L1393.53 0.55724L1405.55 1.31065L1417.14 0.125599L1429.02 3.2962L1440.39 2.22102L1452.05 0.879009L1464.22 4.80301L1477.99 7.0083L1489.79 12.7138L1503.86 9.75511L1516.1 18.2545L1529.37 17.0145L1541.54 14.974L1553.12 17.5011L1565.15 14.3776L1576.88 13.7341L1588.54 15.0761L1600.34 18.5213L1612.37 21.3231L1623.74 14.5973L1635.98 18.2545L1648 18.898L1670.16 9.5903L1683.13 14.3776H1695.15L1703.24 3.35113L1715.7 4.85795L1727.58 9.2136L1738.22 7.76171L1751.19 4.42631L1760.3 12.2822L1770.79 16.8576L1784.35 16.0492L1794.26 21.9666L1808.18 19.7064L1817.65 27.2405L1829.6 29.0691L1842.21 27.7741L1853.87 30.7328L1866.41 28.6924L1879.3 30.8427L1891.91 31.863L1900 31.1095V38.8948Z" fill="white"/>
        </svg>
</section>
<style>/*-- -------------------------- -->
<---            FAQ             -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #faq-1504 {
    padding: var(--sectionPadding);
    position: relative;
    overflow: hidden;
    background-color: #FFF5F3; /* Very light peach background */
    z-index: 1;
  }
  #faq-1504 .cs-container {
    width: 100%;
    /* changes to 1280px at desktop */
    max-width: 36.5rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* 32px - 48px */
    gap: clamp(2rem, 6vw, 3rem);
  }
  #faq-1504 .cs-content {
    /* set text align to left if content needs to be left aligned */
    text-align: center;
    width: 100%;
    display: flex;
    flex-direction: column;
    /* centers content horizontally, set to flex-start to left align */
    align-items: center;
  }
  #faq-1504 .cs-title {
    /* 32px - 48px */
    margin: 0 0 clamp(2rem, 5vw, 3rem);
  }
  #faq-1504 .cs-card-group {
    margin: 0;
    padding: 0;
    display: grid;
    /* changed at desktop */
    grid-template-columns: repeat(auto-fit, minmax(17rem, 1fr));
    /* 16px - 20px */
    gap: clamp(1rem, 1.7vw, 1.25rem);
  }
  #faq-1504 .cs-item {
    text-align: left;
    list-style: none;
    margin: 0;
    padding: 2rem;
    background-color: #fff;
    border: 1px solid #E8E8E8;
    border-radius: 2rem;
  }
  #faq-1504 .cs-number {
    font-size: 3.0625rem;
    line-height: 1.2em;
    font-weight: 900;
    color: var(--primary);
    /* 8px - 72px */
    margin: 0 0 clamp(0.5rem, 6vw, 4.5rem);
    display: block;
  }
  #faq-1504 .cs-item-text {
    font-size: 1rem;
    line-height: 1.5em;
    color: var(--bodyTextColor);
    margin: 0;
  }
  #faq-1504 .cs-image-group {
    position: relative;
    overflow: hidden;
    border-radius: 0 0 5.375rem 5.375rem;
    z-index: 1;
  }
  #faq-1504 .cs-picture {
    width: 100%;
    /* 240px - 376px */
    height: clamp(15rem, 46vw, 23.5rem);
    display: block;
    position: relative;
  }
  #faq-1504 .cs-picture img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  #faq-1504 .cs-mask {
    --maskColor: #FFF5F3; /* Very light peach background */
    height: auto;
    position: absolute;
    top: 0;
    left: -1px;
    right: -1px;
  }
  #faq-1504 .cs-faq-group {
    width: 100%;
    max-width: 40.625rem;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 0.75rem;
  }
  #faq-1504 .cs-faq-item {
    list-style: none;
    width: 100%;
    background-color: #fff;
    border: 1px solid #E8E8E8;
    /* 24px - 32px */
    border-radius: clamp(1rem, 2.4vw, 2rem);
    /* clips all corners of the button that overlap the rounded border */
    overflow: hidden;
    transition: border-color 0.3s;
  }
  #faq-1504 .cs-faq-item.active {
    border-color: var(--secondary);
  }
  #faq-1504 .cs-faq-item.active .cs-button {
    color: var(--secondary);
  }
  #faq-1504 .cs-faq-item.active .cs-button .cs-indicator:before {
    transform: translate(-50%, -50%) rotate(360deg);
  }
  #faq-1504 .cs-faq-item.active .cs-button .cs-indicator:after {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  #faq-1504 .cs-faq-item.active .cs-item-p {
    height: auto;
    /* 20px - 24px bottom */
    /* 16px - 24px left & right */
    padding: 0 clamp(1rem, 2vw, 1.5rem) clamp(1.25rem, 1.3vw, 1.5rem);
    opacity: 1;
  }
  #faq-1504 .cs-button {
    font-size: 1.25rem;
    line-height: 1.2em;
    text-align: left;
    font-weight: bold;
    /* 16px - 24px */
    padding: clamp(1rem, 2vw, 1.5rem);
    background-color: #fff;
    border: none;
    color: var(--headerColor);
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    position: relative;
    transform-style: preserve-3d;
    perspective: 700px;
    transition: background-color 0.3s, color 0.3s;
  }
  #faq-1504 .cs-button:hover {
    cursor: pointer;
  }
  #faq-1504 .cs-button .cs-indicator {
    /* 40px - 48px */
    width: clamp(2.5rem, 4vw, 3rem);
    height: clamp(2.5rem, 4vw, 3rem);
    background-color: #fff;
    border: 1px solid #BABABA;
    border-radius: 50%;
    position: relative;
    z-index: 1;
  }
  #faq-1504 .cs-button .cs-indicator:before {
    /* left line */
    content: '';
    width: 1rem;
    height: 0.125rem;
    background-color: #767676;
    opacity: 1;
    border-radius: 2px;
    position: absolute;
    display: block;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: transform .5s;
  }
  #faq-1504 .cs-button .cs-indicator:after {
    /* right line */
    content: '';
    width: 1rem;
    height: 0.125rem;
    background-color: #767676;
    opacity: 1;
    border-radius: 2px;
    position: absolute;
    display: block;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(90deg);
    transition: transform .5s;
  }
  #faq-1504 .cs-button-text {
    width: 80%;
    display: block;
  }
  #faq-1504 .cs-item-p {
    /* 14px - 16px */
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    line-height: 1.5em;
    width: 90%;
    height: 0;
    margin: 0;
    /* 16px - 24px */
    padding: 0 clamp(1rem, 2vw, 1.5rem);
    opacity: 0;
    color: var(--bodyTextColor);
    /* clips the text so it doesn't show up */
    overflow: hidden;
    transition: opacity 0.3s, padding-bottom 0.3s;
  }
  #faq-1504 .cs-floater {
    display: none;
    position: absolute;
  }
  #faq-1504 .cs-floater1 {
    width: 19.5rem;
    height: auto;
    margin-right: 43.75rem;
    top: 9rem;
    right: 50%;
    transform: rotate(8deg);
  }
  #faq-1504 .cs-floater2 {
    width: 12.9375rem;
    height: auto;
    margin-left: 52.5rem;
    top: 20.625rem;
    left: 50%;
    transform: rotate(8deg);
  }
  #faq-1504 .cs-top {
    width: 100%;
    min-width: 120rem;
    height: auto;
    display: block;
    position: absolute;
    left: 0;
    top: 0;
  }
  #faq-1504 .cs-bottom {
    width: 100%;
    min-width: 120rem;
    height: auto;
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #faq-1504 .cs-container {
    max-width: 80rem;
    flex-direction: row;
    justify-content: space-between;
    align-items: stretch;
  }
  #faq-1504 .cs-content {
    max-height: 38.5rem;
    width: 40%;
    text-align: left;
    align-items: flex-start;
    /* prevents flexbox from squishing it */
    flex: none;
  }
  #faq-1504 .cs-card-group {
    /* pushes up against the other flex items so it pushes to the bottom of the flexbox */
    margin-top: auto;
    grid-template-columns: 1fr 1fr;
  }
  #faq-1504 .cs-item {
    grid-column: span 1;
  }
}
/* Larger Desktop - 1600px */
@media only screen and (min-width: 100rem) {
  #faq-1504 .cs-floater {
    display: block;
  }
}
</style>
<script>const faqItems = Array.from(document.querySelectorAll('.cs-faq-item'));
        for (const item of faqItems) {
            const onClick = () => {
            item.classList.toggle('active')
        }
        item.addEventListener('click', onClick)
        }
</script>